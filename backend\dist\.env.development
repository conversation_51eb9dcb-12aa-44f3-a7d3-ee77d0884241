# Development Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=675b8e8e0039b8b8b8b8
APPWRITE_DATABASE_ID=theaigency_dev_db
APPWRITE_API_KEY=standard_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# Development JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production

# Development Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=dev-db-user
DB_PASSWORD=dev-db-password
DB_NAME=dev-db-name

# Development Server Configuration
PORT=3000
NODE_ENV=development
