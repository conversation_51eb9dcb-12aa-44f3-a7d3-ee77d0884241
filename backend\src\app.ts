import express, { Application, Request, Response, NextFunction, Router } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { v4 as uuidv4 } from 'uuid'; // Import uuidv4
import logger from './config/logger'; // Import logger
import { authenticate } from './middleware/authentication'; // Import authentication middleware

import authRoutes from './auth/auth.controller';
import Paim<PERSON>ontroller from './paim/paim.controller'; // Import PAIM controller
import { AgentController } from './agent/agent.controller'; // Import AgentController
import { agentMiddleware } from './agent/agent.middleware'; // Import agentMiddleware
import aigencyRouter from './aigency/aigency.controller'; // Import AIgency routes
import { AuditController } from './audit/audit.controller'; // Import AuditController
import { auditMiddleware } from './audit/audit.middleware'; // Import auditMiddleware
import { CulturalSensitivityController } from './cultural-sensitivity/cultural-sensitivity.controller'; // Import CulturalSensitivityController
import db from './database/db'; // Import the knex database instance
import dotenv from 'dotenv';
import { PowerOpsController } from './powerops/powerops.controller'; // Import PowerOpsController
import { PowerOpsMiddleware } from './powerops/powerops.middleware'; // Import PowerOpsMiddleware
import { WorkflowCollaborationController } from './workflow-collaboration/workflow-collaboration.controller';
import { WorkflowCollaborationService } from './workflow-collaboration/workflow-collaboration.service';
import { WorkflowCollaborationRepository } from './workflow-collaboration/workflow-collaboration.repository';
import { WorkflowCollaborationMiddleware } from './workflow-collaboration/workflow-collaboration.middleware';
import { OrganizationController } from './organization/organization.controller'; // Import OrganizationController
import { OrganizationService } from './organization/organization.service'; // Import OrganizationService
import { OrganizationRepository } from './organization/organization.repository'; // Import OrganizationRepository
import { EmailService } from './utils/email.service'; // Import EmailService
import { BillingController } from './billing/billing.controller'; // Import BillingController
import { BillingService } from './billing/billing.service'; // Import BillingService
import { BillingRepository } from './billing/billing.repository'; // Import BillingRepository
import AgentFramework from './agent-framework'; // Import Agent Framework
import { NotificationService } from './notifications/notification.service';



// Load environment-specific config first
const envFile = process.env.NODE_ENV === 'production'
  ? '.env.production'
  : '.env.development';

dotenv.config({ path: envFile });

// Fallback to .env
dotenv.config();

const app: Application = express();

// Initialize Agent Framework
const agentFramework = AgentFramework.getInstance();

// Initialize framework with default configuration
agentFramework.initialize().catch(error => {
  logger.error('Failed to initialize Agent Framework:', error);
});

// Initialize NotificationService (needed by other services)
const notificationService = new NotificationService({} as any, {} as any); // Placeholder for now

// Initialize PowerOpsController and Middleware
const powerOpsController = new PowerOpsController(notificationService);
const powerOpsMiddleware = new PowerOpsMiddleware();

// Initialize Workflow Collaboration components
const workflowCollaborationRepository = new WorkflowCollaborationRepository();
// Create placeholder CollaborationEvents for now
const collaborationEvents = {} as any;
// Create placeholder CollaborationSessionService for now
const collaborationSessionService = {} as any;
const workflowCollaborationService = new WorkflowCollaborationService(workflowCollaborationRepository, collaborationEvents, collaborationSessionService);
const workflowCollaborationController = new WorkflowCollaborationController(workflowCollaborationService);
const workflowCollaborationMiddleware = new WorkflowCollaborationMiddleware();

// Initialize PAIM components
const paimController = new PaimController(notificationService, {} as any); // Placeholder for WebSocketService

// Initialize Organization components
const organizationRepository = new OrganizationRepository(db);
const emailService = new EmailService(); // Instantiate EmailService
const organizationService = new OrganizationService(organizationRepository, emailService);
const organizationController = new OrganizationController(organizationService);

// Initialize Billing components
const billingRepository = new BillingRepository(db);
const billingService = new BillingService(billingRepository);
const billingController = new BillingController(billingService);

// Middleware
app.use(express.json()); // Body parser for JSON requests
app.use(cors()); // Enable CORS for all origins (adjust as needed for production)
app.use(helmet()); // Add security headers
app.use(auditMiddleware as express.RequestHandler); // Add global audit middleware
app.use(powerOpsMiddleware.logApiUsage); // Add PowerOps API usage logging middleware

// Middleware to add correlation ID to requests and logs
app.use((req: Request, res: Response, next: NextFunction) => {
  const correlationId = (req as any).headers['x-correlation-id'] || uuidv4();
  (req as any).correlationId = correlationId; // Attach to request object
  logger.defaultMeta = { correlationId }; // Add to default logger metadata
  next();
});

// Health check endpoint (must be before authentication middleware)
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'theaigency-backend'
  });
});

// Rate limiting to prevent brute-force attacks
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again after 15 minutes',
});
app.use(apiLimiter);

// Apply authentication middleware to all routes that require it
// For now, applying globally. In a real app, you might apply it to specific routes or groups of routes.
app.use(authenticate);

// Routes
app.use('/auth', authRoutes);
app.use('/paim-management', paimController.router); // Add PAIM routes
app.use('/aigency', aigencyRouter); // Add AIgency routes

// Workflow Collaboration Routes
app.get('/workflows', workflowCollaborationController.getAllWorkflows);
app.post('/workflows', workflowCollaborationMiddleware.validateCreateWorkflow, workflowCollaborationController.createWorkflow);
app.get('/workflows/:workflowId', workflowCollaborationController.getWorkflowById);
app.put('/workflows/:workflowId', workflowCollaborationController.updateWorkflow);
app.delete('/workflows/:workflowId', workflowCollaborationController.deleteWorkflow);

app.get('/tasks', workflowCollaborationController.getAllTasks);
app.post('/tasks', workflowCollaborationMiddleware.validateCreateTask, workflowCollaborationController.createTask);
app.get('/tasks/:taskId', workflowCollaborationController.getTaskById);
app.put('/tasks/:taskId', workflowCollaborationController.updateTask);
app.delete('/tasks/:taskId', workflowCollaborationController.deleteTask);

app.post('/collaboration/sessions', workflowCollaborationMiddleware.validateStartCollaborationSession, workflowCollaborationController.startCollaborationSession);
app.post('/collaboration/sessions/:sessionId/join', workflowCollaborationController.joinCollaborationSession);
app.post('/collaboration/sessions/:sessionId/leave', workflowCollaborationController.leaveCollaborationSession);

app.post('/cross-tenant-communication/messages', workflowCollaborationMiddleware.validateCrossTenantMessage, workflowCollaborationController.sendCrossTenantMessage);

app.get('/notifications', workflowCollaborationController.getAllNotifications);
app.post('/notifications/:notificationId/read', workflowCollaborationController.markNotificationAsRead);

app.post('/workflows/:workflowId/share', workflowCollaborationMiddleware.validateShareWorkflow, workflowCollaborationController.shareWorkflow);
app.delete('/workflows/:workflowId/share/:permissionId', workflowCollaborationController.deleteWorkflowShare);

app.post('/tasks/:taskId/delegate', workflowCollaborationMiddleware.validateDelegateTask, workflowCollaborationController.delegateTask);

app.get('/workspaces', workflowCollaborationController.getAllWorkspaces);
app.post('/workspaces', workflowCollaborationMiddleware.validateCreateWorkspace, workflowCollaborationController.createWorkspace);
app.get('/workspaces/:workspaceId', workflowCollaborationController.getWorkspaceById);
app.put('/workspaces/:workspaceId', workflowCollaborationController.updateWorkspace);
app.delete('/workspaces/:workspaceId', workflowCollaborationController.deleteWorkspace);

app.get('/teams', workflowCollaborationController.getAllTeams);
app.post('/teams', workflowCollaborationMiddleware.validateCreateTeam, workflowCollaborationController.createTeam);
app.get('/teams/:teamId', workflowCollaborationController.getTeamById);
app.put('/teams/:teamId', workflowCollaborationController.updateTeam);
app.delete('/teams/:teamId', workflowCollaborationController.deleteTeam);

// Add PowerOps routes
app.post('/powerops/usage', powerOpsController.logPowerOpsUsage);
app.get('/powerops/usage', powerOpsController.getPowerOpsUsage);

app.get('/gamification/xp/user/:id', powerOpsController.getXpByUser);
app.get('/gamification/xp/org/:id', powerOpsController.getXpByOrg);
app.post('/gamification/xp/add', powerOpsController.awardXp);

app.get('/gamification/badges', powerOpsController.getAllBadges);
app.get('/gamification/badges/user/:id', powerOpsController.getBadgesByUser);
app.post('/gamification/badges/award', powerOpsController.awardBadge);

app.get('/gamification/achievements', powerOpsController.getAchievements);
app.post('/gamification/achievements', powerOpsController.grantAchievement);

app.get('/gamification/streaks', powerOpsController.getStreaks);

app.get('/cost-management/budgets', powerOpsController.getBudgets);
app.post('/cost-management/budgets', powerOpsController.createBudget);
app.put('/cost-management/budgets/:budgetId', powerOpsController.updateBudget);
app.delete('/cost-management/budgets/:budgetId', powerOpsController.deleteBudget);

app.get('/billing/invoices', powerOpsController.getInvoices);
app.post('/billing/invoices', powerOpsController.createInvoice);

app.post('/billing/payments', powerOpsController.processPayment);

app.get('/gamification/leaderboard', powerOpsController.getLeaderboard);

app.get('/cost-management/recommendations', powerOpsController.getCostOptimizationRecommendations);

app.get('/resource-management/limits', powerOpsController.getResourceUsageLimits);
app.post('/resource-management/limits', powerOpsController.setResourceUsageLimit);

app.get('/notifications', powerOpsController.getNotifications);
app.post('/notifications', powerOpsController.createNotification);

// Initialize AgentController and use its router
const agentController = new AgentController();
app.use('/agents', agentMiddleware, agentController.router); // Add Agent routes with middleware

// Initialize AuditController and use its router
const auditController = new AuditController();
app.use('/audit', auditController.router); // Add Audit routes

// Initialize CulturalSensitivityController and use its router
const culturalSensitivityController = new CulturalSensitivityController(db);
app.use('/cultural-sensitivity', culturalSensitivityController.router); // Add Cultural Sensitivity routes

// Add Billing routes
app.use('/api/v1/billing', billingController.router);

// Add Organization routes
app.use('/api/v1/orgs', organizationController.router);

// Global Error Handler
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Global Error Handler:', err); // Log the error for debugging

  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  // In production, avoid sending sensitive error details to the client
  const errorResponse = {
    timestamp: new Date().toISOString(),
    status: statusCode,
    error: err.name || 'ServerError',
    message: process.env.NODE_ENV === 'production' ? 'Internal Server Error' : message,
    path: (req as any).originalUrl,
    details: err.details || undefined, // Include validation details if available
  };

  (res as any).status(statusCode).json(errorResponse);
});

export default app;